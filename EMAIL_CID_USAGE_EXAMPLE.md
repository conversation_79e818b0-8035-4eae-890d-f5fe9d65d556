# 邮件图片CID转换使用示例

## 修复前的问题

### 问题1：相对路径图片无法显示
```html
<!-- 邮件正文中的相对路径图片 -->
<img src="../../upload/email/image/20250801/55c00f9580824948bb2e96636959b5e3.jpg&random=3" alt="测试图片">
```

**问题**：这种相对路径的图片不会被转换为CID格式，客户端无法显示。

### 问题2：URL参数干扰
```html
<!-- 带URL参数的图片路径 -->
<img src="/upload/email/image/20250801/image.jpg?v=123&random=456" alt="测试图片">
```

**问题**：URL参数会干扰文件路径解析，导致文件找不到。

### 问题3：重复处理
同一个图片文件既作为内嵌图片又作为附件被重复添加到邮件中。

## 修复后的效果

### 1. 相对路径图片正确转换

**输入HTML**：
```html
<img src="../../upload/email/image/20250801/55c00f9580824948bb2e96636959b5e3.jpg&random=3" alt="测试图片">
```

**处理过程**：
1. 清理URL参数：`../../upload/email/image/20250801/55c00f9580824948bb2e96636959b5e3.jpg`
2. 规范化相对路径：`/upload/email/image/20250801/55c00f9580824948bb2e96636959b5e3.jpg`
3. 生成CID：`email_image_20250801_55c00f9580824948bb2e96636959b5e3.jpg`

**输出HTML**：
```html
<img src="cid:email_image_20250801_55c00f9580824948bb2e96636959b5e3.jpg" alt="测试图片">
```

### 2. URL参数正确处理

**输入HTML**：
```html
<img src="/upload/email/image/20250801/image.jpg?v=123&random=456" alt="测试图片">
```

**处理过程**：
1. 清理URL参数：`/upload/email/image/20250801/image.jpg`
2. 生成CID：`email_image_20250801_image.jpg`

**输出HTML**：
```html
<img src="cid:email_image_20250801_image.jpg" alt="测试图片">
```

### 3. 重复处理防护

**场景**：同一个图片既在邮件正文中又作为附件上传

**处理逻辑**：
```java
// 跟踪已处理的图片
Set<String> processedImagePaths = new HashSet<>();

// 处理内嵌图片时标记
processedImagePaths.add(normalizedImagePath);

// 处理附件时检查
if (processedImagePaths.contains(normalizedFilePath)) {
    log.debug("文件已作为内嵌图片处理，跳过作为附件添加: {}", fileName);
    continue;
}
```

**结果**：图片只会作为内嵌图片添加一次，不会重复作为附件。

## 支持的图片路径格式

### 1. 绝对路径
```html
<img src="/upload/email/image/20250801/image.jpg" alt="绝对路径">
```

### 2. 相对路径
```html
<img src="../../upload/email/image/20250801/image.jpg" alt="相对路径">
<img src="../upload/files/image.png" alt="相对路径">
```

### 3. 带URL参数的路径
```html
<img src="/upload/email/image/20250801/image.jpg?v=123" alt="带参数">
<img src="../../upload/email/image/20250801/image.jpg&random=456" alt="带参数">
```

### 4. Base64图片
```html
<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" alt="Base64">
```

### 5. 外部URL（跳过处理）
```html
<img src="http://example.com/image.jpg" alt="外部图片">
<img src="https://example.com/image.png" alt="外部图片">
```

## CID生成规则

### 1. 绝对路径（/upload/开头）
- 输入：`/upload/email/image/20250801/test.jpg`
- CID：`email_image_20250801_test.jpg`

### 2. 相对路径（包含/upload/）
- 输入：`../../upload/email/image/20250801/test.jpg`
- CID：`email_image_20250801_test.jpg`

### 3. 其他路径
- 输入：`images/test.jpg`
- CID：`test.jpg`（使用文件名）

### 4. Base64图片
- CID：`image_[timestamp]_[uuid]`（自动生成唯一ID）

## 日志输出示例

```
[INFO] 添加内嵌图片，fileName: 55c00f9580824948bb2e96636959b5e3.jpg, contentId: email_image_20250801_55c00f9580824948bb2e96636959b5e3.jpg, mimeType: image/jpeg
[DEBUG] 文件已作为内嵌图片处理，跳过作为附件添加: 55c00f9580824948bb2e96636959b5e3.jpg
[INFO] 添加新上传附件: document.pdf
[WARN] 图片文件不存在: D:\webapp\upload\missing\image.jpg
```

## 客户端显示效果

修复后，客户端（如Outlook、Gmail等）能够正确显示邮件中的图片：

1. ✅ 相对路径图片正常显示
2. ✅ 带URL参数的图片正常显示  
3. ✅ 不会出现重复的附件
4. ✅ 邮件大小合理（避免重复添加）

## 兼容性说明

- ✅ 向后兼容原有的绝对路径图片
- ✅ 支持所有主流邮件客户端
- ✅ 支持多种图片格式（jpg、png、gif、webp、bmp等）
- ✅ 支持HEIC/HEIF等新格式（如果系统支持）
