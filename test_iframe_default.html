<!DOCTYPE html>
<html>
<head>
    <title>测试iframe默认显示</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>测试iframe默认显示功能</h1>
    
    <div id="test-results">
        <h2>测试结果：</h2>
        <div id="results"></div>
    </div>

    <script>
        // 复制viewEmail.html中的shouldUseIframe函数来测试
        function shouldUseIframe(htmlContent) {
            // 默认使用iframe显示邮件，提供更好的隔离和显示效果
            // 只有在特殊情况下才使用直接显示模式
            
            // 检查是否为纯文本内容（没有HTML标签）
            const isPlainText = !htmlContent.includes('<') && !htmlContent.includes('>');
            
            // 检查是否为非常简单的HTML（只包含基本格式标签）
            const isVerySimpleHtml = htmlContent.length < 500 && 
                !htmlContent.includes('<style') &&
                !htmlContent.includes('<script') &&
                !htmlContent.includes('<table') &&
                !htmlContent.includes('position:') &&
                !htmlContent.includes('float:') &&
                !/class\s*=/.test(htmlContent) &&
                !/style\s*=/.test(htmlContent);
            
            // 只有纯文本或非常简单的HTML才不使用iframe
            return !(isPlainText || isVerySimpleHtml);
        }

        // 测试用例
        const testCases = [
            {
                name: "纯文本邮件",
                content: "这是一封纯文本邮件，没有任何HTML标签。",
                expectedIframe: false
            },
            {
                name: "简单HTML邮件",
                content: "<p>这是一封简单的HTML邮件</p>",
                expectedIframe: false
            },
            {
                name: "包含样式的HTML邮件",
                content: "<div style='color: red;'>这是包含内联样式的邮件</div>",
                expectedIframe: true
            },
            {
                name: "包含CSS类的HTML邮件",
                content: "<div class='email-content'>这是包含CSS类的邮件</div>",
                expectedIframe: true
            },
            {
                name: "包含表格的HTML邮件",
                content: "<table><tr><td>表格内容</td></tr></table>",
                expectedIframe: true
            },
            {
                name: "包含style标签的HTML邮件",
                content: "<style>body{color:blue;}</style><p>包含样式表的邮件</p>",
                expectedIframe: true
            },
            {
                name: "长内容的HTML邮件",
                content: "<p>" + "这是一封很长的邮件内容。".repeat(100) + "</p>",
                expectedIframe: true
            },
            {
                name: "复杂HTML邮件",
                content: `
                    <html>
                    <head><style>body{font-family:Arial;}</style></head>
                    <body>
                        <div class="header">
                            <h1>邮件标题</h1>
                        </div>
                        <div class="content">
                            <p>邮件内容</p>
                            <table>
                                <tr><td>表格数据</td></tr>
                            </table>
                        </div>
                    </body>
                    </html>
                `,
                expectedIframe: true
            }
        ];

        // 运行测试
        let results = [];
        testCases.forEach(testCase => {
            const actualIframe = shouldUseIframe(testCase.content);
            const passed = actualIframe === testCase.expectedIframe;
            
            results.push({
                name: testCase.name,
                expected: testCase.expectedIframe,
                actual: actualIframe,
                passed: passed
            });
        });

        // 显示结果
        const resultsDiv = document.getElementById('results');
        let html = '<table border="1" style="border-collapse: collapse; width: 100%;">';
        html += '<tr><th>测试用例</th><th>期望使用iframe</th><th>实际使用iframe</th><th>结果</th></tr>';
        
        results.forEach(result => {
            const statusColor = result.passed ? 'green' : 'red';
            const statusText = result.passed ? '✓ 通过' : '✗ 失败';
            
            html += `<tr>
                <td>${result.name}</td>
                <td>${result.expected ? '是' : '否'}</td>
                <td>${result.actual ? '是' : '否'}</td>
                <td style="color: ${statusColor};">${statusText}</td>
            </tr>`;
        });
        
        html += '</table>';
        
        // 统计
        const passedCount = results.filter(r => r.passed).length;
        const totalCount = results.length;
        
        html += `<h3>测试统计：${passedCount}/${totalCount} 通过</h3>`;
        
        if (passedCount === totalCount) {
            html += '<p style="color: green; font-weight: bold;">✓ 所有测试通过！iframe默认显示功能正常工作。</p>';
        } else {
            html += '<p style="color: red; font-weight: bold;">✗ 部分测试失败，请检查代码。</p>';
        }
        
        resultsDiv.innerHTML = html;
    </script>
</body>
</html>
