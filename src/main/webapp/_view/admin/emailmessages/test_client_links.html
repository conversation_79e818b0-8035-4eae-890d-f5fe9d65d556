<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试客户链接功能</title>
    <link href="/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* 客户链接区域样式 - 紧凑版 */
        .client-links-row {
            margin-bottom: 8px;
        }

        .client-links-compact {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 12px;
        }

        .client-company-item {
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .client-company-item:last-child {
            margin-bottom: 0;
        }

        .client-company-name {
            font-weight: 500;
            color: #495057;
            font-size: 12px;
            white-space: nowrap;
            min-width: 0;
            flex-shrink: 0;
        }

        .client-company-links {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
            flex: 1;
        }

        .client-link-btn {
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 3px;
            text-decoration: none;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 3px;
            font-weight: 500;
            border: none;
            line-height: 1.2;
            white-space: nowrap;
        }

        .client-link-btn:hover {
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .client-link-btn i {
            font-size: 10px;
        }

        .client-link-order {
            background: #007bff;
            color: white;
        }

        .client-link-order:hover {
            background: #0056b3;
            color: white;
        }

        .client-link-document {
            background: #28a745;
            color: white;
        }

        .client-link-document:hover {
            background: #1e7e34;
            color: white;
        }

        .client-link-customer {
            background: #ffc107;
            color: #212529;
        }

        .client-link-customer:hover {
            background: #e0a800;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>客户链接功能测试</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>测试邮件信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label>发件人邮箱:</label>
                        <input type="text" id="fromAddress" class="form-control" value="<EMAIL>" placeholder="输入发件人邮箱">
                    </div>
                    <div class="col-md-4">
                        <label>收件人邮箱:</label>
                        <input type="text" id="toAddress" class="form-control" value="<EMAIL>" placeholder="输入收件人邮箱">
                    </div>
                    <div class="col-md-4">
                        <label>抄送人邮箱:</label>
                        <input type="text" id="ccAddress" class="form-control" value="<EMAIL>" placeholder="输入抄送人邮箱">
                    </div>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="testClientLinks()">测试客户链接</button>
                    <button type="button" class="btn btn-secondary" onclick="clearResults()">清空结果</button>
                </div>
            </div>
        </div>
        
        <!-- 客户链接区域 -->
        <div class="client-links-row mt-4" id="clientLinksRow" style="display: none;">
            <div class="client-links-compact">
                <div id="clientLinksContent">
                    <!-- 客户链接将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 调试信息 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>调试信息</h5>
            </div>
            <div class="card-body">
                <pre id="debugInfo"></pre>
            </div>
        </div>
    </div>

    <script src="/assets/plugins/jquery/jquery.min.js"></script>
    <script src="/assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script>
        function testClientLinks() {
            const fromAddress = document.getElementById('fromAddress').value;
            const toAddress = document.getElementById('toAddress').value;
            const ccAddress = document.getElementById('ccAddress').value;
            
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.textContent = '正在测试客户链接功能...\n';
            debugInfo.textContent += `发件人: ${fromAddress}\n`;
            debugInfo.textContent += `收件人: ${toAddress}\n`;
            debugInfo.textContent += `抄送人: ${ccAddress}\n\n`;
            
            // 显示加载状态
            const clientLinksRow = document.getElementById('clientLinksRow');
            const clientLinksContent = document.getElementById('clientLinksContent');
            clientLinksContent.innerHTML = '<div class="text-center py-2"><i class="fa fa-spinner fa-spin"></i> 正在查找相关客户...</div>';
            clientLinksRow.style.display = 'block';
            
            $.ajax({
                url: '/admin/emailMessages/getCompaniesFromEmails',
                type: 'GET',
                data: {
                    fromAddress: fromAddress,
                    toAddress: toAddress,
                    ccAddress: ccAddress
                },
                dataType: 'json',
                success: function(response) {
                    debugInfo.textContent += '请求成功!\n';
                    debugInfo.textContent += '响应数据: ' + JSON.stringify(response, null, 2) + '\n';
                    
                    if (response.state === 'ok' && response.data && response.data.length > 0) {
                        displayClientLinks(response.data);
                        debugInfo.textContent += `找到 ${response.data.length} 个相关公司\n`;
                    } else {
                        clientLinksContent.innerHTML = '<div class="text-muted">未找到相关客户公司</div>';
                        debugInfo.textContent += '未找到相关客户公司\n';
                    }
                },
                error: function(xhr, status, error) {
                    debugInfo.textContent += '请求失败!\n';
                    debugInfo.textContent += `状态: ${status}\n`;
                    debugInfo.textContent += `错误: ${error}\n`;
                    debugInfo.textContent += `响应: ${xhr.responseText}\n`;
                    
                    clientLinksContent.innerHTML = '<div class="text-danger"><i class="fa fa-exclamation-triangle"></i> 查找客户信息失败</div>';
                }
            });
        }
        
        function displayClientLinks(companies) {
            const clientLinksContent = document.getElementById('clientLinksContent');

            let html = '';
            companies.forEach(function(company, index) {
                const companyName = escapeHtml(company.name || '未知公司');
                const nickName = escapeHtml(company.nick_name || '');
                const osclubId = company.osclub_id || '';

                html += '<div class="client-company-item">';

                // 显示公司名称（简化版）
                let displayName = companyName;
                if (nickName && nickName !== companyName) {
                    displayName = nickName; // 优先显示简称
                }
                // 限制长度，避免占用太多空间
                if (displayName.length > 12) {
                    displayName = displayName.substring(0, 12) + '...';
                }
                html += '<span class="client-company-name" title="' + companyName + (nickName ? ' (' + nickName + ')' : '') + '">' + displayName + ':</span>';

                html += '<div class="client-company-links">';

                let hasLinks = false;

                // 订单页面链接
                if (nickName) {
                    html += '<a href="http://360.theolympiastone.com/my/dd?query=' + encodeURIComponent(nickName) + '" target="_blank" class="client-link-btn client-link-order" title="' + companyName + ' - 查看订单信息">';
                    html += '<i class="fa fa-shopping-cart"></i>订单';
                    html += '</a>';

                    // 单证页面链接
                    html += '<a href="http://360.theolympiastone.com/my/dzhy?query=' + encodeURIComponent(nickName) + '" target="_blank" class="client-link-btn client-link-document" title="' + companyName + ' - 查看单证信息">';
                    html += '<i class="fa fa-file-text"></i>单证';
                    html += '</a>';

                    hasLinks = true;
                }

                // 客户页面链接
                if (osclubId) {
                    html += '<a href="http://360.theolympiastone.com/my/kh/edit?id=' + osclubId + '" target="_blank" class="client-link-btn client-link-customer" title="' + companyName + ' - 查看客户详情">';
                    html += '<i class="fa fa-user"></i>客户';
                    html += '</a>';

                    hasLinks = true;
                }

                // 如果没有可用链接，显示提示
                if (!hasLinks) {
                    html += '<span class="text-muted" style="font-size: 10px;">无链接</span>';
                }

                html += '</div>';
                html += '</div>';
            });

            clientLinksContent.innerHTML = html;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function clearResults() {
            document.getElementById('clientLinksRow').style.display = 'none';
            document.getElementById('debugInfo').textContent = '';
        }
    </script>
</body>
</html>
