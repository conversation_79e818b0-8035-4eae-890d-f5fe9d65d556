package cn.jbolt.mail.gpt.parser;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.Maps;
import com.jfinal.kit.PropKit;

import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailAttachments;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.common.util.StringKit;
import jakarta.mail.Flags;
import jakarta.mail.Folder;
import jakarta.mail.Message;
import jakarta.mail.UIDFolder;
import jakarta.mail.internet.MimeMessage;

/**
 * 邮件解析服务
 * 负责协调多个解析器解析邮件
 */
public class EmailParsingService {
    private static final Logger LOG = LoggerFactory.getLogger(EmailParsingService.class);
    private static final Tika tika = new Tika();

    // 使用 ConcurrentHashMap 作为线程安全的缓存
    private final Map<Message, Map<String, Object>> parseCache = new ConcurrentHashMap<>();

    // 缓存过期时间（可选）
    private final long cacheExpiryTimeInMillis = 60 * 60 * 1000; // 1小时

    // 可选：缓存条目的时间戳
    private final Map<Message, Long> cacheTimestamps = new ConcurrentHashMap<>();

    // 调度器，用于定期清理过期缓存
    private final ScheduledExecutorService scheduler;

    public EmailParsingService() {
        // 初始化调度器并启动定期清理任务
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "EmailParsing-CacheCleanup");
            thread.setDaemon(true); // 使用守护线程，不阻止JVM退出
            return thread;
        });

        // 启动定期清理任务，例如每5分钟执行一次
        scheduler.scheduleAtFixedRate(
                this::cleanupExpiredCache,
                5,
                5,
                TimeUnit.MINUTES);

    }

    /**
     * 手动清除所有缓存
     */
    public void clearCache() {
        parseCache.clear();
        cacheTimestamps.clear();
    }

    /**
     * 关闭服务，清理资源
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        clearCache();
    }

    /**
     * 获取缓存的解析结果
     *
     * @param message   邮件对象
     * @param parseType 解析类型（例如："subject", "body", "attachments"等）
     * @return 缓存的解析结果，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    private <T> T getCachedResult(Message message, String parseType) {
        // 检查缓存是否过期
        Long timestamp = cacheTimestamps.get(message);
        if (timestamp != null && System.currentTimeMillis() - timestamp > cacheExpiryTimeInMillis) {
            // 缓存已过期，清除
            parseCache.remove(message);
            cacheTimestamps.remove(message);
            return null;
        }

        Map<String, Object> messageCache = parseCache.get(message);
        if (messageCache != null) {
            return (T) messageCache.get(parseType);
        }
        return null;
    }

    /**
     * 清理所有过期的缓存项
     */
    private void cleanupExpiredCache() {
        try {
            long currentTime = System.currentTimeMillis();

            // 创建要移除的项的列表，避免在遍历时修改集合
            List<Message> expiredMessages = new ArrayList<>();

            for (Map.Entry<Message, Long> entry : cacheTimestamps.entrySet()) {
                if (currentTime - entry.getValue() > cacheExpiryTimeInMillis) {
                    expiredMessages.add(entry.getKey());
                }
            }

            // 移除所有过期项
            for (Message message : expiredMessages) {
                parseCache.remove(message);
                cacheTimestamps.remove(message);
            }

            if (!expiredMessages.isEmpty()) {
                // 可以添加日志记录清理了多少缓存项
                // logger.debug("Cleaned up {} expired cache entries", expiredMessages.size());
            }
        } catch (Exception e) {
            // 捕获并记录任何异常，确保清理任务不会中断
            // logger.error("Error during cache cleanup", e);
        }
    }

    /**
     * 缓存解析结果
     *
     * @param message   邮件对象
     * @param parseType 解析类型
     * @param result    解析结果
     */
    private <T> void cacheResult(Message message, String parseType, T result) {
        parseCache.computeIfAbsent(message, k -> new ConcurrentHashMap<>()).put(parseType, result);
        cacheTimestamps.put(message, System.currentTimeMillis());
    }

    public static String getEmailAttachmentsPath() {
        String emailReceiveFolder = PropKit.get("email_receive_folder", "d:/attachments/");
        if (!emailReceiveFolder.endsWith("/")) {
            emailReceiveFolder += "/";
        }
        return emailReceiveFolder;
    }

    public static String getEmailFailedPath() {
        String emailFailedFolder = PropKit.get("email_failed_folder", "d:/attachments/failed");
        if (!emailFailedFolder.endsWith("/")) {
            emailFailedFolder += "/";
        }
        return emailFailedFolder;
    }

    /**
     * 创建附件保存路径
     *
     * @param emailMessages 邮件消息对象
     * @param contentId     内容ID
     * @return 附件保存路径
     */
    public static String createAttachmentPath(EmailMessages emailMessages, String contentId) {
        String emailReceiveFolder = PropKit.get("email_receive_folder", "d:/attachments/");
        if (!emailReceiveFolder.endsWith("/")) {
            emailReceiveFolder += "/";
        }
        Date date = emailMessages.getSentDate();
        if (date != null) {
            emailReceiveFolder += new SimpleDateFormat("yyyyMM/dd").format(date);
        }
        String attachmentPath = emailReceiveFolder + "/" + emailMessages.getAccountId()
                + "/" + emailMessages.getId();

        // 如果有contentId且不为空，则创建专门的CID目录
        if (StringUtils.isNotEmpty(contentId)) {
            attachmentPath = attachmentPath + File.separator + "cid";
        }

        return attachmentPath;
    }

    /**
     * 创建附件保存路径
     *
     * @param emailMessages 邮件消息对象
     * @return 附件保存路径
     */
    public static String createScreenshotPath(EmailMessages emailMessages) {
        String emailScreenShotFolder = PropKit.get("email_screenshot_folder", "d:/attachments/screenshots/")
                .replace("\\\\", "/").replace("\\", "/").replaceAll("/$", "") + "/";
        if (!emailScreenShotFolder.endsWith(File.separator)) {
            emailScreenShotFolder += File.separator;
        }
        Date date = emailMessages.getSentDate();
        if (date != null) {
            emailScreenShotFolder += new SimpleDateFormat("yyyyMM/dd").format(date);
        }
        return emailScreenShotFolder + File.separator + emailMessages.getAccountId()
                + File.separator + emailMessages.getId() + File.separator;
    }

    /**
     * 获取邮件的UID
     * 如果文件夹支持UIDFolder接口，直接使用接口获取UID
     * 如果不支持，尝试使用反射获取
     * 如果上述方法都失败，使用邮件序号作为替代
     *
     * @param folder  文件夹
     * @param message 邮件
     * @return UID或邮件序号
     */
    public long getUID(Folder folder, Message message) {
        Long cacheUid = getCachedResult(message, "uid");
        if (cacheUid != null) {
            return cacheUid;
        }

        // 先检查文件夹是否支持UIDFolder接口
        if (folder instanceof UIDFolder) {
            try {
                UIDFolder uidFolder = (UIDFolder) folder;
                long uid = uidFolder.getUID(message);
                cacheResult(message, "uid", uid);
                return uid;
            } catch (Exception e) {
                // 静默处理异常，继续尝试其他方法
            }
        }

        // 尝试使用反射获取UID
        try {
            java.lang.reflect.Method getUIDMethod = folder.getClass().getMethod("getUID", Message.class);
            Object uid = getUIDMethod.invoke(folder, message);
            if (uid != null) {
                long uidReflect = Long.parseLong(uid.toString());
                cacheResult(message, "uid", uidReflect);
                return uidReflect;
            }
        } catch (Exception e) {
            // 静默处理异常，不打印警告日志
        }

        // 如果上述方法都失败，使用邮件序号作为替代
        try {
            return message.getMessageNumber();
        } catch (Exception ex) {
            // 静默处理异常，不打印警告日志
            cacheResult(message, "uid", -1);
            return -1;
        }
    }

    /**
     * 获取邮件标志字符串
     *
     * @param message 邮件
     * @return 标志字符串
     */
    public String getFlagsString(Message message) {
        String cacheFlags = getCachedResult(message, "flags");
        if (cacheFlags != null) {
            return cacheFlags;
        }

        try {
            Flags flags = message.getFlags();
            StringBuilder sb = new StringBuilder();

            if (flags.contains(Flags.Flag.SEEN))
                sb.append("SEEN ");
            if (flags.contains(Flags.Flag.ANSWERED))
                sb.append("ANSWERED ");
            if (flags.contains(Flags.Flag.DELETED))
                sb.append("DELETED ");
            if (flags.contains(Flags.Flag.FLAGGED))
                sb.append("FLAGGED ");
            if (flags.contains(Flags.Flag.DRAFT))
                sb.append("DRAFT ");
            if (flags.contains(Flags.Flag.RECENT))
                sb.append("RECENT ");

            String[] userFlags = flags.getUserFlags();
            for (String flag : userFlags) {
                sb.append(flag).append(" ");
            }

            String flagsString = sb.toString().trim();
            cacheResult(message, "flags", flagsString);
            return flagsString;
        } catch (Exception e) {
            LOG.warn("获取邮件标志失败: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 创建EmailMessages对象
     *
     * @param account 邮箱账户
     * @param message 邮件
     * @return EmailMessages对象
     */
    public EmailMessages createEmailMessageObject(EmailAccount account, Message message) {
        // 创建邮件记录
        Folder folder = message.getFolder();
        String messageId = parseMessageId(message);
        String messageHash = generateMessageHash(message);
        EmailMessages emailMessage = new EmailMessages();
        emailMessage.setEmailAccount(account.getUsername());
        emailMessage.setAccountId(account.getId());
        emailMessage.setFolderName(folder.getFullName());
        emailMessage.setMessageId(messageId);
        emailMessage.setMessageHash(messageHash);
        emailMessage.setUidValue(getUID(folder, message));
        emailMessage.setMessageIdHeader(parseMessageIdFull(message));
        emailMessage.setSubject(parseSubject(message));
        String fromAddress = parseFrom(message);
        emailMessage.setFromDisplay(StringKit.getDisplayName(fromAddress));
        emailMessage.setFromAddress(fromAddress);
        List<String> toList = parseTo(message);
        emailMessage.setToAddress(String.join(", ", toList));
        emailMessage.setToDisplay(StringKit.getDisplayName(toList));
        emailMessage.setCcAddress(String.join(", ", parseCC(message)));
        emailMessage.setBccAddress(String.join(", ", parseBCC(message)));
        emailMessage.setContentType(parseContentType(message));
        try {
            emailMessage.setSentDate(message.getSentDate());
        } catch (Exception e) {
            LOG.warn("获取发送时间失败: {}", e.getMessage());
            emailMessage.setSentDate(new Date()); // 使用当前时间作为默认值
        }

        try {
            emailMessage.setReceivedDate(message.getReceivedDate());
        } catch (Exception e) {
            LOG.warn("获取接收时间失败: {}", e.getMessage());
            emailMessage.setReceivedDate(new Date()); // 使用当前时间作为默认值
        }

        try {
            emailMessage.setSize(message.getSize());
        } catch (Exception e) {
            LOG.warn("获取邮件大小失败: {}", e.getMessage());
            emailMessage.setSize(0); // 默认大小为0
        }

        try {
            // 使用解析服务检查附件
            emailMessage.setHasAttachments(hasAttachments(message));
        } catch (Exception e) {
            LOG.warn("检查附件失败: {}", e.getMessage());
            emailMessage.setHasAttachments(false); // 默认没有附件
        }

        emailMessage.setFetchStatus(0); // 仅元数据

        // 设置内部文件夹ID
        try {
            // 使用反射获取 UIDValidity
            String internalFolderId = "0";
            try {
                java.lang.reflect.Method getUIDValidityMethod = folder.getClass().getMethod("getUIDValidity");
                Object uidValidity = getUIDValidityMethod.invoke(folder);
                internalFolderId = uidValidity.toString();
            } catch (Exception e) {
                LOG.warn("无法获取文件夹 UIDValidity: {}", e.getMessage());
            }
            emailMessage.setInternalFolderId(internalFolderId);
        } catch (Exception e) {
            LOG.warn("设置内部文件夹ID失败: {}", e.getMessage());
        }

        // 设置创建和更新时间
        Date now = new Date();
        emailMessage.setCreatedAt(now);
        emailMessage.setUpdatedAt(now);

        return emailMessage;
    }

    public String parseMessageId(Message message) {
        String cacheMessageId = getCachedResult(message, "messageId");
        if (cacheMessageId != null) {
            return cacheMessageId;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                String messageId = parser.parseMessageId(message);
                if (messageId != null) {
                    cacheResult(message, "messageId", messageId);
                    return messageId;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件MessageId: {}", parser.getParserName(), e.getMessage());
            }
        }

        LOG.warn("所有解析器都无法解析MessageId，返回默认值");
        cacheResult(message, "messageId", "无法解析的MessageId");
        return "[无法解析的MessageId]";
    }

    public String parseMessageIdFull(Message message) {
        String messsageIdFull = getCachedResult(message, "messageIdFull");
        if (messsageIdFull != null) {
            return messsageIdFull;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                String messageIdFull = parser.parseMessageIdFull(message);
                if (messageIdFull != null) {
                    cacheResult(message, "messageIdFull", messageIdFull);
                    return messageIdFull;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件MessageIdFull: {}", parser.getParserName(), e.getMessage());
            }
        }

        LOG.warn("所有解析器都无法解析MessageIdFull，返回默认值");
        cacheResult(message, "messageIdFull", "[无法解析的MessageIdFull]");
        return "[无法解析的MessageIdFull]";
    }

    /**
     * 解析邮件主题，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 邮件主题，如果所有解析器都失败则返回默认值
     */
    public String parseSubject(Message message) {
        String cacheSubject = getCachedResult(message, "subject");
        if (cacheSubject != null) {
            return cacheSubject;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        String lastAttemptedSubject = null;
        for (EmailParserAbstract parser : parsers) {
            try {
                String subject = parser.parseSubject(message);
                if (subject != null && !subject.trim().isEmpty()) {
                    cacheResult(message, "subject", subject);
                    return subject;
                }
                // 记录最后一次尝试的结果，即使是空字符串
                if (subject != null) {
                    lastAttemptedSubject = subject;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件主题失败: {}", parser.getParserName(), e.getMessage());
            }
        }

        // 尝试直接从Message对象获取主题作为最后的备用方案
        try {
            String directSubject = message.getSubject();
            if (directSubject != null && !directSubject.trim().isEmpty()) {
                LOG.info("使用直接方法成功获取邮件主题: {}", directSubject);
                cacheResult(message, "subject", directSubject);
                return directSubject;
            }
        } catch (Exception e) {
            LOG.warn("直接获取邮件主题也失败: {}", e.getMessage());
        }

        // 如果有空字符串结果，优先使用空字符串而不是错误标记
        if (lastAttemptedSubject != null) {
            LOG.warn("所有解析器都无法解析邮件主题，使用空主题");
            cacheResult(message, "subject", lastAttemptedSubject);
            return lastAttemptedSubject;
        }

        LOG.warn("所有解析器都无法解析邮件主题，返回默认值");
        cacheResult(message, "subject", "[无法解析的主题]");
        return "[无法解析的主题]";
    }

    /**
     * 解析邮件发件人，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 发件人地址，如果所有解析器都失败则返回默认值
     */
    public String parseFrom(Message message) {
        String cacheFrom = getCachedResult(message, "from");
        if (cacheFrom != null) {
            return cacheFrom;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                String from = parser.parseFrom(message);
                if (from != null) {
                    cacheResult(message, "from", from);
                    return from;
                }
            } catch (Exception e) {
                LOG.warn(parser.getParserName() + " 解析邮件发件人失败: " + e.getMessage());
            }
        }

        LOG.warn("所有解析器都无法解析邮件发件人，返回默认值");
        cacheResult(message, "from", "");
        return "";
    }

    public String parsePersonal(Message message) {
        String cachePersonal = getCachedResult(message, "personal");
        if (cachePersonal != null) {
            return cachePersonal;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                String personal = parser.parsePersonal(message);
                if (personal != null) {
                    cacheResult(message, "personal", personal);
                    return personal;
                }
            } catch (Exception e) {
                LOG.warn(parser.getParserName() + " 解析邮件发件人失败: " + e.getMessage());
            }
        }

        LOG.warn("所有解析器都无法解析邮件发件人，返回默认值");
        String from = parseFrom(message);
        cacheResult(message, "personal", from);
        return from;
    }

    /**
     * 解析邮件收件人，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 收件人地址列表
     */
    public List<String> parseTo(Message message) {
        List<String> cacheTo = getCachedResult(message, "to");
        if (cacheTo != null) {
            return cacheTo;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                List<String> to = parser.parseTo(message);
                if (to != null && !to.isEmpty()) {
                    cacheResult(message, "to", to);
                    return to;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件收件人失败: {}", parser.getParserName(), e.getMessage());
            }
        }

        LOG.warn("所有解析器都无法解析邮件收件人，返回空列表");
        cacheResult(message, "to", new ArrayList<>());
        return new ArrayList<>();
    }

    /**
     * 解析邮件抄送人，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 抄送人地址列表
     */
    public List<String> parseCC(Message message) {
        List<String> cacheCc = getCachedResult(message, "cc");
        if (cacheCc != null) {
            return cacheCc;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                List<String> cc = parser.parseCC(message);
                if (cc != null && !cc.isEmpty()) {
                    cacheResult(message, "cc", cc);
                    return cc;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件抄送人失败: {}", parser.getParserName(), e.getMessage());
            }
        }
        cacheResult(message, "cc", new ArrayList<>());
        return new ArrayList<>();
    }

    /**
     * 解析邮件密送人，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 密送人地址列表
     */
    public List<String> parseBCC(Message message) {
        List<String> cacheBcc = getCachedResult(message, "bcc");
        if (cacheBcc != null) {
            return cacheBcc;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                List<String> bcc = parser.parseBCC(message);
                if (bcc != null && !bcc.isEmpty()) {
                    return bcc;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件密送人失败: {}", parser.getParserName(), e.getMessage());
            }
        }

        cacheResult(message, "bcc", new ArrayList<>());
        return new ArrayList<>();
    }

    /**
     * 解析邮件内容类型，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 内容类型
     */
    public String parseContentType(Message message) {
        String cacheContentType = getCachedResult(message, "contentType");
        if (cacheContentType != null) {
            return cacheContentType;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                String contentType = parser.parseContentType(message);
                if (contentType != null) {
                    cacheResult(message, "contentType", contentType);
                    return contentType;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件内容类型失败: {}", parser.getParserName(), e.getMessage());
            }
        }

        LOG.warn("所有解析器都无法解析邮件内容类型，返回默认值");
        cacheResult(message, "contentType", "text/plain");
        return "text/plain";
    }

    /**
     * 检查邮件是否有附件，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 是否有附件
     */
    public boolean hasAttachments(Message message) {
        Boolean cacheHasAttachments = getCachedResult(message, "hasAttachments");
        if (cacheHasAttachments != null) {
            return cacheHasAttachments;
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                boolean hasAttachments = parser.hasAttachments(message);
                cacheResult(message, "hasAttachments", hasAttachments);
                return hasAttachments;
            } catch (Exception e) {
                LOG.warn("{} 检查邮件附件失败: {}", parser.getParserName(), e.getMessage());
            }
        }

        // 如果所有解析器都失败，保存原始邮件数据以便后续分析
        saveRawEmailForAnalysis(message);

        LOG.warn("所有解析器都无法检查邮件附件，默认假设有附件");
        cacheResult(message, "hasAttachments", true);
        return true; // 保守起见，假设有附件
    }

    /**
     * 解析邮件附件，尝试所有可用解析器
     * 增强版本：添加了更严格的附件类型检查，排除邮件正文部分
     *
     * @param message 邮件对象
     */
    public void parseAttachments(Message message, EmailMessages emailMessage) {
        EmailParserJakarta emailParserJakarta = new EmailParserJakarta();
        try {
            emailParserJakarta.parseAttachments(message, emailMessage);
        } catch (Exception e) {
            LOG.warn("{} 处理邮件附件失败: {} {}", emailParserJakarta.getParserName(), emailMessage.getLogInfo(),
                    e.getMessage());
        }
    }

    /**
     * 解析邮件正文，尝试所有可用解析器
     *
     * @param message 邮件对象
     * @return 邮件正文
     */
    public Map<String, String> parseBody(Message message) {
        Map<String, String> cacheBody = getCachedResult(message, "body");
        if (cacheBody != null) {
            return cacheBody;
        }
        if (message == null) {
            return Maps.newHashMap();
        }
        List<EmailParserAbstract> parsers = EmailParserFactory.getCompatibleParsers(message);

        for (EmailParserAbstract parser : parsers) {
            try {
                Map<String, String> body = parser.parseBody(message);
                if (body != null && !body.isEmpty()) {
                    cacheResult(message, "body", body);
                    return body;
                }
            } catch (Exception e) {
                LOG.warn("{} 解析邮件正文失败: {}", parser.getParserName(), e.getMessage());
            }
        }

        LOG.warn("所有解析器都无法解析邮件正文，返回空字符串");
        cacheResult(message, "body", Maps.newHashMap());
        return Maps.newHashMap();
    }

    /**
     * 保存原始邮件数据以便后续分析
     *
     * @param message 邮件对象
     */
    private void saveRawEmailForAnalysis(Message message) {
        try {
            // 生成文件名
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String messageId = "unknown";

            try {
                String[] headers = message.getHeader("Message-ID");
                if (headers != null && headers.length > 0) {
                    messageId = headers[0].replaceAll("[<>]", "").replaceAll("[\\\\/:*?\"<>|]", "_");
                }
            } catch (Exception e) {
                LOG.warn("获取邮件ID失败: {}", e.getMessage());
            }

            String fileName = "unprocessable_email_" + messageId + "_" + timestamp + ".eml";
            File dir = new File(getEmailFailedPath());
            FileUtils.forceMkdir(dir);
            File file = new File(dir, fileName);

            // 保存为.eml文件
            for (EmailParserAbstract parser : EmailParserFactory.getCompatibleParsers(message)) {
                try (InputStream is = parser.getRawInputStream(message);
                        FileOutputStream fos = new FileOutputStream(file)) {
                    if (is != null) {
                        byte[] buffer = new byte[8192];
                        while (is.read(buffer) != -1) {
                            fos.write(buffer);
                        }
                        fos.flush();
                        LOG.info("已保存无法处理的邮件: {}", file.getAbsolutePath());
                        break;
                    }
                } catch (Exception e) {
                    LOG.warn("{} 保存原始邮件数据失败: {}", parser.getParserName(), e.getMessage());
                }
            }
        } catch (Exception e) {
            LOG.error("保存原始邮件数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成邮件Hash
     *
     * @param message 邮件
     * @return 邮件Hash
     */
    public String generateMessageHash(Message message) {
        String cacheMessageHash = getCachedResult(message, "messageHash");
        if (cacheMessageHash != null) {
            return cacheMessageHash;
        }
        try {
            String fromAddress = parseFrom(message);
            String toAddress = StringUtils.join(parseTo(message), ",");
            Date sentDate = message.getSentDate();
            String sentDateStr = sentDate != null ? Long.toString(sentDate.getTime()) : "";
            String subject = parseSubject(message);

            // 组合字符串
            String combined = fromAddress + "|" + toAddress + "|" + sentDateStr + "|" + subject;

            // 计算MD5
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(combined.getBytes());

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            String hex = hexString.toString();
            cacheResult(message, "messageHash", hex);
            return hex;
        } catch (Exception e) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            LOG.debug("降级方案 生成邮件Hash成功: 随机 UUID");
            cacheResult(message, "messageHash", uuid);
            return uuid;
        }
    }

    /**
     * 保存无法处理的邮件
     *
     * @param message 邮件对象
     * @param uid     UID
     */
    public void saveUnprocessableEmail(Message message, int accountId, long uid) {
        try {
            // 生成唯一标识
            String messageId = "unknown_" + uid;
            try {
                messageId = parseMessageId(message);
                // 处理 messageId 中的非法字符，确保安全用于文件路径
                messageId = messageId.replaceAll("[\\\\/:*?\"<>|\t\n\r\f\0\u0000-\u001F\u007F\u0080-\u009F\u00A0]+",
                        "_");
            } catch (Exception e) {
                LOG.warn("无法解析邮件ID: " + e.getMessage());
            }

            // 创建保存目录
            String dir = getEmailFailedPath();
            FileUtils.forceMkdir(new File(dir));

            // 生成文件名
            String fileName = "unprocessable_email_" + messageId + ".eml";
            File file = new File(dir, fileName);

            // 保存为.eml文件
            if (message instanceof MimeMessage) {
                try (FileOutputStream fos = new FileOutputStream(file)) {
                    message.writeTo(fos);
                    fos.flush();
                    LOG.info("已保存无法处理的邮件: {}", file.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            LOG.error("保存无法处理的邮件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理HTML正文中的CID引用，将它们替换为本地文件路径
     * 
     * @param htmlContent   HTML正文内容
     * @param emailMessages 邮件消息对象
     * @return 处理后的HTML内容
     */
    public String processCidReferences(String htmlContent, EmailMessages emailMessages) {
        if (StringUtils.isEmpty(htmlContent)) {
            return htmlContent;
        }

        try {
            // 创建HTML解析器
            Document doc = Jsoup.parse(htmlContent);

            // 查询该邮件的所有附件记录
            List<EmailAttachments> attachments = new EmailAttachments().dao().find(
                    "SELECT * FROM email_attachments WHERE email_id = ? AND cid IS NOT NULL",
                    emailMessages.getId());

            // 创建CID到文件路径的映射
            Map<String, String> cidToFilePathMap = new HashMap<>();
            for (EmailAttachments attachment : attachments) {
                if (StringUtils.isNotEmpty(attachment.getCid())) {
                    cidToFilePathMap.put(attachment.getCid(), attachment.getPath());
                }
                if (StringUtils.isNotEmpty(attachment.getContentIdFull())) {
                    cidToFilePathMap.put(attachment.getContentIdFull(), attachment.getPath());
                }
            }

            // 1. 处理所有带src属性的元素(img, iframe等)
            Elements srcElements = doc.select("[src^=cid:]");
            for (Element element : srcElements) {
                String cidRef = element.attr("src").substring(4); // 去掉"cid:"前缀
                replaceCidWithFilePath(element, "src", cidRef, cidToFilePathMap);
            }

            // 2. 处理所有带href属性的元素
            Elements hrefElements = doc.select("[href^=cid:]");
            for (Element element : hrefElements) {
                String cidRef = element.attr("href").substring(4);
                replaceCidWithFilePath(element, "href", cidRef, cidToFilePathMap);
            }

            // 3. 处理其他可能包含cid的属性(background, data等)
            processOtherCidAttributes(doc, "background", cidToFilePathMap);
            processOtherCidAttributes(doc, "data", cidToFilePathMap);
            processOtherCidAttributes(doc, "poster", cidToFilePathMap);

            // 4. 处理内联样式中的CID引用
            Elements styledElements = doc.select("[style*=cid:]");
            for (Element element : styledElements) {
                String style = element.attr("style");
                if (style.contains("cid:")) {
                    for (Map.Entry<String, String> entry : cidToFilePathMap.entrySet()) {
                        String cidPattern = "url\\(['\"]?cid:" + Pattern.quote(entry.getKey()) + "['\"]?\\)";
                        String replacement = "url('/common/file?filePath=" + entry.getValue() + "')";
                        style = style.replaceAll(cidPattern, replacement);
                    }
                    element.attr("style", style);
                }
            }

            return doc.body().html();
        } catch (Exception e) {
            LOG.error("处理CID引用出错", e);
            return htmlContent;
        }
    }

    /**
     * 替换元素的CID引用为本地文件路径
     */
    private void replaceCidWithFilePath(Element element, String attribute, String cidRef,
            Map<String, String> cidToFilePathMap) {
        for (String cidKey : cidToFilePathMap.keySet()) {
            if (cidRef.equals(cidKey) || cidRef.startsWith(cidKey)) {
                String filePath = cidToFilePathMap.get(cidKey);
                element.attr(attribute, "/common/file?filePath=" + filePath);

                // 如果是图片，添加限制宽度的样式
                if (element.tagName().equals("img")) {
                    element.attr("style", "max-width:400px; height:auto;");
                }

                return;
            }
        }

        // 如果没有找到匹配的CID，可以选择保留原样或添加提示
        LOG.debug("未找到匹配的CID文件: {}", cidRef);
    }

    /**
     * 处理其他属性中的CID引用
     */
    private void processOtherCidAttributes(Document doc, String attribute, Map<String, String> cidToFilePathMap) {
        Elements elements = doc.select("[" + attribute + "^=cid:]");
        for (Element element : elements) {
            String cidRef = element.attr(attribute).substring(4);
            replaceCidWithFilePath(element, attribute, cidRef, cidToFilePathMap);
        }
    }
}
